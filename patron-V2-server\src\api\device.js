import device from '../models/device.js';
import mongoose from 'mongoose';

export const getDevices = async (req, res) => {
    let filter={}
    if(req.query.userId){
     filter={userId:req.query.userId.split(',')}
    }
    let data = await device.find(filter);
    res.send(data);
}

export const getDevicespatronpal = async (req, res) => {
    let filter={}
    if(req.query.userId){
     filter={userId:req.query.userId.split(',')}
    }
    let data = await device.find(filter).populate("userId").populate({
        path: 'reviews.customerId',
        select: 'FirstName LastName profile_pic'
    }).populate({
        path: 'favorites.customerId',
        select: 'FirstName LastName profile_pic'
    })
    .exec();
    res.send(data);
}

export const getDevice = async (req, res) => {
    let data = await device.findOne(req.params);
    res.send(data);
}

export const postDevice = async (req, res) => {
    const { active, name, userId, Line1, Line2, City, Phoneno, State, PostalCode, Country,delivery, deliveryStartTime, deliveryEndTime, ChargesperKm, ChargesFreeKm, pickupStartTime, pickupEndTime, businessType , Streetaddress} = req.body;
    const image = req.file ? req.file.location : null
    const data = await new device({ active, name, userId, Line1, Line2, City, Phoneno, State, PostalCode, Country, image, delivery, deliveryStartTime, deliveryEndTime, ChargesperKm, ChargesFreeKm, pickupStartTime, pickupEndTime, businessType ,Streetaddress});
    await data.save().then(result => {
        console.log(result, "Device data save to database")
        res.json({
            _id: result._id,
            name: result.name,
            active: result.active,
            userId: result.userId,
            image: result.image,
            Line1: result.Line1,
            line2: result.Line2,
            City: result.City,
            Phoneno: result.Phoneno,
            State: result.State,
            PostalCode: result.PostalCode,
            Country:result.Country,
            delivery: result.delivery,
            deliveryStartTime: result.deliveryStartTime,
            deliveryEndTime: result.deliveryEndTime,
            ChargesperKm: result.ChargesperKm, 
            ChargesFreeKm: result.ChargesFreeKm,
            pickupStartTime: result.pickupStartTime,
            pickupEndTime: result.pickupEndTime,
            businessType: result.businessType,
            Streetaddress: result.Streetaddress,
        })
    }).catch(err => {
        res.status(400).send('unable to save database');
        console.log(err)
    })
}
export const updateDevice = async (req, res) => {
    console.log(req.params._id)
    let image
    if(req.file){
         image = req.file ? req.file.location : null
    }
    console.log("body /: ", req.body)

    let data = await device.findByIdAndUpdate(
        { _id: req.params._id },
        {
            $set: req.body, image: image,
        },  { new: true });
    if (data) {
        res.send({ data, message: "device data updated successfully" });
    }
    else {
        res.send({ message: "device data cannot be updated successfully" })
    }
}

// Get device information for review form by userId
export const getDeviceForReview = async (req, res) => {
    const { userId } = req.params;

    // Validate the userId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ message: "Invalid user ID format." });
    }

    try {
        // Find the device by userId
        const Device = await device.findOne({ userId }).populate('userId', 'name email businessName');
        if (!Device) {
            return res.status(404).json({ message: "Device not found for this user" });
        }

        // Return device information needed for review form
        const deviceInfo = {
            _id: Device._id,
            name: Device.name,
            businessName: Device.userId?.businessName || Device.name,
            address: `${Device.Line1 || ''} ${Device.Line2 || ''}, ${Device.City || ''}, ${Device.State || ''} ${Device.PostalCode || ''}`.trim(),
            phone: Device.Phoneno,
            image: Device.image,
            userId: Device.userId._id,
            userEmail: Device.userId.email
        };

        res.json({
            success: true,
            device: deviceInfo,
            message: "Device information retrieved successfully"
        });
    } catch (error) {
        console.error("Error getting device for review:", error);
        res.status(500).json({ message: "Server error" });
    }
};


export const postReview = async (req, res) => {
    const { deviceId, food, service, ambiance, testimonial,  customerId } = req.body;

    // Validate the deviceId
    if (!mongoose.Types.ObjectId.isValid(deviceId)) {
        return res.status(400).json({ message: "Invalid device ID format." });
    }

    try {
        // Find the device by ID
        const Device = await device.findById(deviceId);
        if (!Device) {
            return res.status(404).json({ message: "Device not found" });
        }

        // Check if a review from the same customer already exists
        const existingReview = Device.reviews.find(review => review.customerId == customerId);
        if (existingReview) {
            return res.status(400).json({ message: "Review from this customer already exists." });
        }

        // Create and add the new review
        const newReview = {
            food,
            service,
            ambiance,
            testimonial,
            customerId
        };

        Device.reviews.push(newReview);
        await Device.save();

        res.json({ message: "Review added successfully", review: newReview });
    } catch (error) {
        console.error("Error adding review:", error);
        res.status(500).json({ message: "Server error" });
    }
};


// Get all devices where any review has the given customerId    
export const getallcustomer = async (req, res) => {
    const { customerId } = req.params;

    // Validate customerId
    if (!mongoose.Types.ObjectId.isValid(customerId)) {
        return res.status(400).json({ message: "Invalid customer ID format." });
    }

    try {
        // Find all devices where reviews contain the given customerId
        let devices = await device.find({
            'reviews.customerId': customerId
        }).populate({
            path: 'reviews.customerId',
            select: 'FirstName LastName profile_pic' // Adjust as needed to include necessary fields
        });

        // Filter reviews to only include those with the matching customerId
        devices = devices.map(device => {
            device.reviews = device.reviews.filter(review => {
                return review.customerId._id.toString() === customerId;
            });
            return device;
        });

        res.json(devices);
    } catch (error) {
        console.error("Error fetching devices:", error);
        res.status(500).json({ message: "Server error" });
    }
};
// deviceRouter.put('/:deviceId/favorites/:customerId',
export const addFavorite = async (req, res) => {
    try {
      const { deviceId, customerId } = req.params; // Destructure params for clarity
      
      console.log("deviceId, customerId : " , deviceId, customerId);
      // Validate required parameters
      if (!deviceId || !customerId) {
        return res.status(400).json({ error: 'Missing required parameters: deviceId and customerId' });
      }

      const Device = await device.findById(deviceId); // Find the device by ID
  
      if (!Device) {
        return res.status(404).json({ error: 'Device not found' });
      }
  
      const existingFavorite = Device.favorites.some(favorite => favorite.customerId.toString() == customerId);
  
      if (existingFavorite) {
        // If the device is already in favorites, remove it
        Device.favorites = Device.favorites.filter(favorite => favorite.customerId.toString() !== customerId);
      } else {
          
          // If the device is not in favorites, add it
          Device.favorites.push({ customerId: customerId });
        }
        console.log("Device : ",Device);
        
      await Device.save(); // Save the changes to the device
  
      const updatedDevice = await device.findById(Device).populate('favorites.customerId'); // Populate customer details
  
      res.status(200).json({ status: true, data: updatedDevice });
    } catch (error) {
      console.error(error); // Log the error for debugging
      res.status(500).json({ error: 'Internal server error' });
    }
  };
  
  export const getFavoriteByCutomerId = async (req, res) => {
    try {
      const { customerId } = req.params;
  
      // Validate required parameter
      if (!customerId) {
        return res.status(400).json({ error: 'Missing required parameter: customerId' });
      }
  
      const Devices = await device.find().populate('favorites.customerId');
      console.log("Devices : ",Devices);
      
      const favoriteDevices = Devices.filter(device => device.favorites.some(favorite =>   favorite?.customerId?._id.toString() === customerId));
  
      res.json(favoriteDevices);
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Internal server error' });
    }
  };

// export const getallcustomer = async (req, res) => {
//     const { customerId } = req.params;

//     // Validate customerId
//     if (!mongoose.Types.ObjectId.isValid(customerId)) {
//         return res.status(400).json({ message: "Invalid customer ID format." });
//     }

//     try {
//         // Find all devices where reviews contain the given customerId
//         let devices = await device.find({
//             'reviews.customerId': customerId
//         }).populate({
//             path: 'reviews.customerId',
//             select: 'FirstName LastName profile_pic' // Adjust as needed to include necessary fields
//         });

//         // Filter reviews to only include those with the matching customerId
//         devices = devices.map(device => {
//             device.reviews = device.reviews
//                 .filter(review => review.customerId._id.toString() === customerId)
//                 .map(review => {
//                     // Clone the review object to avoid mutating Mongoose documents directly
//                     const clonedReview = {
//                         ...review.toObject(),
//                         // Calculate the total points and average score
//                         averageScore: ((review.food + review.service + review.ambiance) / 3).toFixed(2)
//                     };
//                     return clonedReview;
//                 });

//             return device;
//         });

//         res.json(devices);
//     } catch (error) {
//         console.error("Error fetching devices:", error);
//         res.status(500).json({ message: "Server error" });
//     }
// };




export const deleteDevice = async (req, res) => {
    console.log(req.params)
    let data = await device.deleteOne(req.params)
    if (data) {
        res.send({ message: "device data delete successfully" });
    }
    else {
        res.send({ message: "device data cannot delete successfully" })
    }
}